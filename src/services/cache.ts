import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { ICacheService } from './interfaces';
import { CacheEntry, CacheStorage, CacheOptions } from '../interfaces/types';
import { getExtensionContext } from '../extension';
import { log } from '../utils/logger';
import { FILE_SYSTEM } from '../constants';

/**
 * Generic cache service for API requests and other data
 * Provides file-based caching with TTL support and automatic cleanup
 */
export class CacheService implements ICacheService {
  private cacheFilePath: string | null = null;

  /**
   * Get the cache file path
   */
  private async getCacheFilePath(): Promise<string> {
    if (!this.cacheFilePath) {
      const context = getExtensionContext();
      this.cacheFilePath = path.join(context.extensionPath, FILE_SYSTEM.CACHE_FILES.API_REQUESTS);
    }
    return this.cacheFilePath;
  }

  /**
   * Load cache storage from file
   */
  private async loadCacheStorage(): Promise<CacheStorage> {
    try {
      const cachePath = await this.getCacheFilePath();

      if (fs.existsSync(cachePath)) {
        const cacheData = fs.readFileSync(cachePath, FILE_SYSTEM.UTF8_ENCODING);
        const storage: CacheStorage = JSON.parse(cacheData);

        log('[Cache] Loaded cache storage', {
          entries: Object.keys(storage).length,
          path: cachePath,
        });

        return storage;
      }
    } catch (error: any) {
      log('[Cache] Error loading cache storage', error.message, true);
    }

    return {};
  }

  /**
   * Save cache storage to file
   */
  private async saveCacheStorage(storage: CacheStorage): Promise<void> {
    try {
      const cachePath = await this.getCacheFilePath();
      fs.writeFileSync(cachePath, JSON.stringify(storage, null, 2), FILE_SYSTEM.UTF8_ENCODING);

      log('[Cache] Saved cache storage', {
        entries: Object.keys(storage).length,
        path: cachePath,
      });
    } catch (error: any) {
      log('[Cache] Error saving cache storage', error.message, true);
      throw error;
    }
  }

  /**
   * Check if a cache entry is valid (not expired)
   */
  private isValidEntry(entry: CacheEntry): boolean {
    const now = Date.now();
    const isValid = now - entry.timestamp < entry.ttl;

    if (!isValid) {
      log('[Cache] Entry expired', {
        key: entry.key,
        age: Math.round((now - entry.timestamp) / 1000) + 's',
        ttl: Math.round(entry.ttl / 1000) + 's',
      });
    }

    return isValid;
  }

  /**
   * Get cached data by key
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const storage = await this.loadCacheStorage();
      const entry = storage[key];

      if (!entry) {
        log('[Cache] Cache miss', { key });
        return null;
      }

      if (!this.isValidEntry(entry)) {
        log('[Cache] Cache expired, removing entry', { key });
        await this.delete(key);
        return null;
      }

      log('[Cache] Cache hit', {
        key,
        age: Math.round((Date.now() - entry.timestamp) / 1000) + 's',
      });

      return entry.data as T;
    } catch (error: any) {
      log('[Cache] Error getting cached data', error.message, true);
      return null;
    }
  }

  /**
   * Set cached data with TTL
   */
  async set<T>(key: string, data: T, options: CacheOptions): Promise<void> {
    try {
      const storage = await this.loadCacheStorage();

      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl: options.ttl,
        key,
      };

      storage[key] = entry;
      await this.saveCacheStorage(storage);

      log('[Cache] Data cached', {
        key,
        ttl: Math.round(options.ttl / 1000) + 's',
        dataSize: JSON.stringify(data).length + ' bytes',
      });
    } catch (error: any) {
      log('[Cache] Error caching data', error.message, true);
      throw error;
    }
  }

  /**
   * Check if a cache entry exists and is valid
   */
  async has(key: string): Promise<boolean> {
    const data = await this.get(key);
    return data !== null;
  }

  /**
   * Remove a specific cache entry
   */
  async delete(key: string): Promise<void> {
    try {
      const storage = await this.loadCacheStorage();

      if (storage[key]) {
        delete storage[key];
        await this.saveCacheStorage(storage);
        log('[Cache] Entry deleted', { key });
      }
    } catch (error: any) {
      log('[Cache] Error deleting cache entry', error.message, true);
      throw error;
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    try {
      await this.saveCacheStorage({});
      log('[Cache] All cache entries cleared');
    } catch (error: any) {
      log('[Cache] Error clearing cache', error.message, true);
      throw error;
    }
  }

  /**
   * Clean up expired cache entries
   */
  async cleanup(): Promise<void> {
    try {
      const storage = await this.loadCacheStorage();
      const keys = Object.keys(storage);
      let removedCount = 0;

      for (const key of keys) {
        const entry = storage[key];
        if (!this.isValidEntry(entry)) {
          delete storage[key];
          removedCount++;
        }
      }

      if (removedCount > 0) {
        await this.saveCacheStorage(storage);
        log('[Cache] Cleanup completed', {
          removed: removedCount,
          remaining: Object.keys(storage).length,
        });
      } else {
        log('[Cache] No expired entries to clean up');
      }
    } catch (error: any) {
      log('[Cache] Error during cleanup', error.message, true);
      throw error;
    }
  }

  /**
   * Generate a cache key for API requests
   */
  generateKey(endpoint: string, params?: Record<string, any>, userId?: string): string {
    const keyData = {
      endpoint,
      params: params || {},
      userId: userId || 'anonymous',
    };

    const keyString = JSON.stringify(keyData);
    const hash = crypto.createHash('sha256').update(keyString).digest('hex');

    // Use first 16 characters of hash for readability
    const shortHash = hash.substring(0, 16);

    log('[Cache] Generated cache key', {
      endpoint,
      userId: userId || 'anonymous',
      key: shortHash,
    });

    return shortHash;
  }
}

// Create a singleton instance for use throughout the application
export const cacheService = new CacheService();
