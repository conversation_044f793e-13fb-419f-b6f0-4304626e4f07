{"statusBar": {"premiumFastRequests": "Premium Fast Requests", "usageBasedPricing": "Usage-Based Pricing", "teamSpend": "Team Spend", "period": "Period", "utilized": "utilized", "used": "used", "remaining": "remaining", "limit": "limit", "spent": "spent", "of": "of", "perDay": "per day", "dailyRemaining": "daily remaining", "weekdaysOnly": "weekdays only", "today": "today", "isWeekend": "is weekend", "cursorUsageStats": "Cursor Usage Stats", "errorState": "Error State", "enabled": "Enabled", "disabled": "Disabled", "noUsageRecorded": "No usage recorded for this period", "usageBasedDisabled": "Usage-based pricing is currently disabled", "errorCheckingStatus": "Error checking usage-based pricing status", "unableToCheckStatus": "Unable to check usage-based pricing status", "unpaidAmount": "Unpaid: {amount}", "youHavePaid": "ℹ️ You have paid **{amount}** of this cost already", "accountSettings": "Account <PERSON><PERSON>", "currency": "<PERSON><PERSON><PERSON><PERSON>", "extensionSettings": "Extension Settings", "refresh": "Refresh", "noTokenFound": "Cursor Stats: No token found", "couldNotRetrieveToken": "⚠️ Could not retrieve Cursor token from database", "requestsUsed": "requests used", "fastRequestsPeriod": "Fast Requests Period", "usageBasedPeriod": "Usage Based Period", "currentUsage": "Current Usage", "total": "Total", "unpaid": "Unpaid", "discounted": "discounted", "unknownModel": "unknown-model", "unknownItem": "Unknown Item", "totalCost": "Total Cost", "noUsageDataAvailable": "No usage data available", "usage": "Usage", "weekday": "weekday", "weekdays": "weekdays", "month": "Month", "apiUnavailable": "Cursor API Unavailable (Retrying in {countdown})", "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}}, "progressBar": {"errorParsingDates": "Error parsing dates", "dailyRemainingLimitReached": "📊 Daily Remaining: 0 requests/day (limit reached)", "dailyRemainingWeekend": "📊 Daily Remaining: Weekend - calculations resume Monday", "dailyRemainingPeriodEnding": "📊 Daily Remaining: Period ending soon", "dailyRemainingCalculation": "📊 Daily Remaining: {requestsPerDay} requests/{dayType}\n    ({remainingRequests} requests ÷ {remainingDays} {dayTypePlural})"}, "api": {"midMonthPayment": "Mid-month payment", "toolCalls": "tool calls", "fastPremium": "fast premium", "requestUnit": "req"}, "github": {"preRelease": "Pre-release", "stableRelease": "Stable release", "latest": "Latest", "updateAvailable": "{releaseType} {releaseName} is available! You are on {currentVersion}", "changesTitle": "{releaseType} {version} Changes", "sourceCodeZip": "Source code (zip)", "sourceCodeTarGz": "Source code (tar.gz)", "viewFullRelease": "View full release on GitHub", "installedMessage": "Cursor Stats {version} has been installed"}, "notifications": {"usageThresholdReached": "Premium request usage has reached {percentage}%", "usageExceededLimit": "Premium request usage has exceeded limit ({percentage}%)", "spendingThresholdReached": "Your Cursor usage spending has reached {amount}", "unpaidInvoice": "⚠️ You have an unpaid mid-month invoice. Please pay it to continue using usage-based pricing.", "enableUsageBasedTitle": "Enable Usage-Based", "enableUsageBasedDetail": "Enable usage-based pricing to continue using premium models.", "viewSettingsTitle": "View Settings", "viewSettingsDetail": "Click View Settings to manage your usage limits.", "manageLimitTitle": "Manage Limit", "manageLimitDetail": "Click Manage Limit to adjust your usage-based pricing settings.", "nextNotificationAt": "Next spending notification at {amount}.", "currentTotalCost": "Current total usage cost is {amount}.", "payInvoiceToContinue": "Please pay your invoice to continue using usage-based pricing.", "openBillingPage": "Open Billing Page", "dismiss": "<PERSON><PERSON><PERSON>", "unknownModelsDetected": "New or unhandled Cursor model terms detected: \"{models}\". If these seem like new models, please create a report and submit it on GitHub.", "usageBasedSpendingThreshold": "Usage-based spending has reached {percentage}% of your ${limit} limit", "failedToOpenSettings": "Failed to open Cursor Stats settings. Please try opening VS Code settings manually."}, "commands": {"refreshStats": "Cursor Stats: Refresh Statistics", "openSettings": "Cursor Stats: Open Settings", "setLimit": "Cursor Stats: Usage Based Pricing Settings", "selectCurrency": "Cursor Stats: Select Display Currency", "createReport": "Cursor Stats: Generate Diagnostic Report", "enableUsageBased": "Enable Usage-Based Pricing", "setMonthlyLimit": "Set Monthly Limit", "disableUsageBased": "Disable Usage-Based Pricing", "selectLanguage": "Cursor Stats: Select Language", "languageChanged": "Language changed to {language}. Interface will update automatically.", "openGitHubIssues": "Open GitHub Issues", "createReportProgress": "Creating Cursor Stats Report", "gatheringData": "Gathering data...", "completed": "Completed!", "reportCreatedSuccessfully": "Report created successfully!\n{fileName}", "openFile": "Open File", "openFolder": "Open Folder", "enableUsageBasedOption": "$(check) Enable Usage-Based Pricing", "enableUsageBasedDescription": "Turn on usage-based pricing and set a limit", "setMonthlyLimitOption": "$(pencil) Set Monthly Limit", "setMonthlyLimitDescription": "Change your monthly spending limit", "disableUsageBasedOption": "$(x) Disable Usage-Based Pricing", "disableUsageBasedDescription": "Turn off usage-based pricing", "enterMonthlyLimit": "Enter monthly spending limit in dollars", "enterNewMonthlyLimit": "Enter new monthly spending limit in dollars", "validNumberRequired": "Please enter a valid number greater than 0", "usageBasedEnabledWithLimit": "Usage-based pricing enabled with {limit} limit", "usageBasedAlreadyEnabled": "Usage-based pricing is already enabled", "limitUpdatedTo": "Monthly limit updated to {limit}", "enableUsageBasedFirst": "Please enable usage-based pricing first", "usageBasedDisabled": "Usage-based pricing disabled", "usageBasedAlreadyDisabled": "Usage-based pricing is already disabled", "failedToManageLimit": "Failed to manage usage limit: {error}", "currentStatus": "Current status: {status} {limit}", "selectCurrencyPrompt": "Select currency for display", "currentLanguagePrompt": "Current: {language}. Select a language for Cursor Stats interface", "selectLanguagePrompt": "Select Language / 选择语言 / 언어 선택"}, "settings": {"enableUsageBasedPricing": "Enable Usage-Based Pricing", "changeMonthlyLimit": "Change Monthly Limit", "disableUsageBasedPricing": "Disable Usage-Based Pricing", "enableUsageBasedDescription": "Turn on usage-based pricing and set a limit", "setLimitDescription": "Change your monthly spending limit", "disableUsageBasedDescription": "Turn off usage-based pricing", "currentLimit": "Current limit: ${limit}", "enterNewLimit": "Enter new monthly limit (in USD)", "invalidLimit": "Please enter a valid number greater than 0", "limitUpdated": "Usage limit updated successfully to ${limit}", "signInRequired": "Please sign in to <PERSON><PERSON><PERSON> first", "updateFailed": "Failed to update usage limit"}, "errors": {"tokenNotFound": "Session token not found. Please sign in to Cursor.", "apiError": "API request failed", "databaseError": "Database access error", "networkError": "Network connection error", "updateFailed": "Failed to update statistics", "unknownError": "An unknown error occurred", "failedToCreateReport": "Failed to create report. Check logs for details.", "errorCreatingReport": "Error creating report: {error}"}, "time": {"day": "day", "days": "days", "hour": "hour", "hours": "hours", "minute": "minute", "minutes": "minutes", "second": "second", "seconds": "seconds", "ago": "ago", "refreshing": "Refreshing...", "lastUpdated": "Last Updated"}, "currency": {"usd": "US Dollar", "eur": "Euro", "gbp": "British Pound", "jpy": "Japanese Yen", "aud": "Australian Dollar", "cad": "Canadian Dollar", "chf": "Swiss Franc", "cny": "Chinese Yuan", "inr": "Indian Rupee", "mxn": "Mexican Peso", "brl": "Brazilian Real", "rub": "Russian Ruble", "krw": "South Korean Won", "sgd": "Singapore Dollar", "nzd": "New Zealand Dollar", "try": "Turkish Lira", "zar": "South African Rand", "sek": "Swedish Krona", "nok": "Norwegian Krone", "dkk": "Danish Krone", "hkd": "Hong Kong Dollar", "twd": "Taiwan Dollar", "php": "Philippine Peso", "thb": "Thai Baht", "idr": "Indonesian Rupiah", "vnd": "Vietnamese Dong", "ils": "Israeli Shekel", "aed": "UAE Dirham", "sar": "Saudi Riyal", "myr": "Malaysian Ringgit", "pln": "Polish Złoty", "czk": "Czech Koruna", "huf": "Hungarian Forint", "ron": "Romanian Leu", "bgn": "Bulgarian Lev", "hrk": "Croatian Kuna", "egp": "Egyptian Pound", "qar": "Qatari Riyal", "kwd": "<PERSON><PERSON>", "mad": "Moroccan <PERSON><PERSON><PERSON>"}}