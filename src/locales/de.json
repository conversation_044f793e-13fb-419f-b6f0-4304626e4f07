{"statusBar": {"premiumFastRequests": "Premium-Anfragen", "usageBasedPricing": "Nutzungsbasierte Abrechnung", "teamSpend": "Team-<PERSON><PERSON><PERSON>", "period": "Zeitraum", "utilized": "gen<PERSON>t", "used": "ver<PERSON><PERSON>", "remaining": "verbleibend", "limit": "Limit", "spent": "ausgegeben", "of": "von", "perDay": "pro Tag", "dailyRemaining": "täglich verbleibend", "weekdaysOnly": "nur Wochentage", "today": "heute", "isWeekend": "ist Wochenende", "cursorUsageStats": "Cursor Nutzungsstatistiken", "errorState": "Fehlerzustand", "enabled": "Aktiviert", "disabled": "Deaktiviert", "noUsageRecorded": "Keine Nutzung für diesen Zeitraum aufgezeichnet", "usageBasedDisabled": "Nutzungsbasierte Preisgestaltung ist derzeit deaktiviert", "errorCheckingStatus": "Fehler beim Überprüfen des Status der nutzungsbasierten Preisgestaltung", "unableToCheckStatus": "Status der nutzungsbasierten Preisgestaltung kann nicht überprüft werden", "unpaidAmount": "Unbezahlt: {amount}", "youHavePaid": "ℹ️ Sie haben bereits **{amount}** von diesen Kosten bezahlt", "accountSettings": "Kontoeinstellungen", "currency": "Währung", "extensionSettings": "Erweiterungseinstellungen", "refresh": "Aktualisieren", "noTokenFound": "Cursor Stats: <PERSON><PERSON> gefunden", "couldNotRetrieveToken": "⚠️ Cursor Token konnte nicht aus der Datenbank abgerufen werden", "requestsUsed": "<PERSON><PERSON><PERSON> verwendet", "fastRequestsPeriod": "<PERSON><PERSON>elle Anfragen Zeitraum", "usageBasedPeriod": "Nutzungsbasierter Zeitraum", "currentUsage": "Aktuelle Nutzung", "total": "Gesamt", "unpaid": "Unbezahlt", "discounted": "<PERSON>uz<PERSON><PERSON>", "unknownModel": "unbekanntes-Modell", "unknownItem": "Unbekanntes Element", "totalCost": "Gesamtkosten", "noUsageDataAvailable": "<PERSON><PERSON> Nutzungsdaten verfügbar", "usage": "<PERSON><PERSON>ung", "weekday": "Wochentag", "weekdays": "Wochentage", "month": "<PERSON><PERSON>", "apiUnavailable": "Cursor API nicht verfügbar (Neuer Versuch in {countdown})", "months": {"january": "<PERSON><PERSON><PERSON>", "february": "<PERSON><PERSON><PERSON>", "march": "<PERSON><PERSON><PERSON>", "april": "April", "may": "<PERSON>", "june": "<PERSON><PERSON>", "july": "<PERSON><PERSON>", "august": "August", "september": "September", "october": "Oktober", "november": "November", "december": "Dezember"}}, "progressBar": {"errorParsingDates": "<PERSON><PERSON> be<PERSON>", "dailyRemainingLimitReached": "📊 Täglich verbleibend: 0 Anfragen/Tag (Limit erreicht)", "dailyRemainingWeekend": "📊 Täglich verbleibend: Wochenende - Berechnungen werden am Montag fortgesetzt", "dailyRemainingPeriodEnding": "📊 Täglich verbleibend: Zeitraum endet bald", "dailyRemainingCalculation": "📊 Täglich verbleibend: {requestsPerDay} Anfragen/{dayType}\n    ({remainingRequests} Anfragen ÷ {remainingDays} {dayTypePlural})"}, "api": {"midMonthPayment": "Monatsmitte-Zahlung", "toolCalls": "Tool-Aufrufe", "fastPremium": "schnell premium", "requestUnit": "Anf"}, "github": {"preRelease": "Vorabversion", "stableRelease": "Stabile Version", "latest": "Neueste", "updateAvailable": "{releaseType} {releaseName} ist verfügbar! Sie verwenden {currentVersion}", "changesTitle": "{releaseType} {version} Änderungen", "sourceCodeZip": "Quellcode (zip)", "sourceCodeTarGz": "Quellcode (tar.gz)", "viewFullRelease": "Vollständige Version auf GitHub anzeigen", "installedMessage": "Cursor Stats {version} wurde installiert"}, "notifications": {"usageThresholdReached": "Premium-Anfragen-Nutzung hat {percentage}% erreicht", "usageExceededLimit": "Premium-Anfragen-Nutzung hat das Limit überschritten ({percentage}%)", "spendingThresholdReached": "Ihre Cursor-Nutzungsausgaben haben {amount} erreicht", "unpaidInvoice": "⚠️ Sie haben eine unbezahlte Monatsmitte-Rechnung. Bitte bezahlen Sie diese, um die nutzungsbasierte Abrechnung weiter zu nutzen.", "enableUsageBasedTitle": "Nutzungsbasiert aktivieren", "enableUsageBasedDetail": "Aktivieren Sie die nutzungsbasierte Abrechnung, um Premium-Modelle weiter zu nutzen.", "viewSettingsTitle": "Einstellungen anzeigen", "viewSettingsDetail": "Klicken Sie auf Einstellungen anzeigen, um Ihre Nutzungslimits zu verwalten.", "manageLimitTitle": "<PERSON><PERSON> ver<PERSON>ten", "manageLimitDetail": "Klicken Sie auf Limit verwalten, um Ihre nutzungsbasierten Preiseinstellungen anzupassen.", "nextNotificationAt": "Nächste Ausgaben-Benachrichtigung bei {amount}.", "currentTotalCost": "Aktuelle Gesamtnutzungskosten betragen {amount}.", "payInvoiceToContinue": "Bitte bezahlen Sie Ihre Rechnung, um die nutzungsbasierte Abrechnung weiter zu nutzen.", "openBillingPage": "Abrechnungsseite öffnen", "dismiss": "Verwerfen", "unknownModelsDetected": "Neue oder unbehandelte Cursor-Modellbegriffe erkannt: \"{models}\". Falls diese wie neue Modelle aussehen, erstellen Si<PERSON> bitte einen Bericht und reichen Si<PERSON> ihn auf GitHub ein.", "usageBasedSpendingThreshold": "Nutzungsbasierte Ausgaben haben {percentage}% Ihres ${limit} Limits erreicht", "failedToOpenSettings": "Fehler beim Öffnen der Cursor Stats-Einstellungen. Bitte versuchen Sie, die VS Code-Einstellungen manuell zu öffnen."}, "commands": {"refreshStats": "Cursor Stats: Statistiken aktualisieren", "openSettings": "Cursor Stats: Einstellungen öffnen", "setLimit": "Cursor Stats: Nutzungsbasierte Preiseinstellungen", "selectCurrency": "Cursor Stats: Anzeigewährung auswählen", "createReport": "Cursor Stats: Diagnosebericht erstellen", "enableUsageBased": "Nutzungsbasierte Abrechnung aktivieren", "setMonthlyLimit": "Monatliches Limit festlegen", "disableUsageBased": "Nutzungsbasierte Abrechnung deaktivieren", "selectLanguage": "Cursor Stats: Sprache auswählen", "languageChanged": "Sprache geändert zu {language}. Die Benutzeroberfläche wird automatisch aktualisiert.", "openGitHubIssues": "GitHub Issues <PERSON>", "createReportProgress": "Cursor Stats-Be<PERSON>t wird erstellt", "gatheringData": "Daten werden gesammelt...", "completed": "Abgeschlossen!", "reportCreatedSuccessfully": "Bericht erfolgreich erstellt!\n{fileName}", "openFile": "<PERSON><PERSON>", "openFolder": "Ordner öffnen", "enableUsageBasedOption": "$(check) Nutzungsbasierte Abrechnung aktivieren", "enableUsageBasedDescription": "Nutzungsbasierte Abrechnung aktivieren und ein Limit festlegen", "setMonthlyLimitOption": "$(pencil) Monatliches Limit festlegen", "setMonthlyLimitDescription": "Ihr monatliches Ausgabenlimit ändern", "disableUsageBasedOption": "$(x) Nutzungsbasierte Abrechnung deaktivieren", "disableUsageBasedDescription": "Nutzungsbasierte Abrechnung ausschalten", "enterMonthlyLimit": "Monatliches Ausgabenlimit in Dollar eingeben", "enterNewMonthlyLimit": "Neues monatliches Ausgabenlimit in Dollar eingeben", "validNumberRequired": "Bitte geben Sie eine gültige Zahl größer als 0 ein", "usageBasedEnabledWithLimit": "Nutzungsbasierte Abrechnung aktiviert mit {limit} Limit", "usageBasedAlreadyEnabled": "Nutzungsbasierte Abrechnung ist bereits aktiviert", "limitUpdatedTo": "Monatliches Limit aktualisiert auf {limit}", "enableUsageBasedFirst": "Bitte aktivieren Sie zuerst die nutzungsbasierte Abrechnung", "usageBasedDisabled": "Nutzungsbasierte Abrechnung deaktiviert", "usageBasedAlreadyDisabled": "Nutzungsbasierte Abrechnung ist bereits deaktiviert", "failedToManageLimit": "Fehler beim Verwalten des Nutzungslimits: {error}", "currentStatus": "Aktueller Status: {status} {limit}", "selectCurrencyPrompt": "Währung für die Anzeige auswählen", "currentLanguagePrompt": "Aktuell: {language}. Wählen Sie eine Sprache für die Cursor Stats-Benutzeroberfläche", "selectLanguagePrompt": "Sprache auswählen / Select Language / 选择语言 / 언어 선택"}, "settings": {"enableUsageBasedPricing": "Nutzungsbasierte Abrechnung aktivieren", "changeMonthlyLimit": "Monatliches Limit ändern", "disableUsageBasedPricing": "Nutzungsbasierte Abrechnung deaktivieren", "enableUsageBasedDescription": "Nutzungsbasierte Abrechnung aktivieren und ein Limit festlegen", "setLimitDescription": "Ihr monatliches Ausgabenlimit ändern", "disableUsageBasedDescription": "Nutzungsbasierte Abrechnung ausschalten", "currentLimit": "Aktuelles Limit: ${limit}", "enterNewLimit": "Neues monatliches Limit eingeben (in USD)", "invalidLimit": "Bitte geben Sie eine gültige Zahl größer als 0 ein", "limitUpdated": "Nutzungslimit erfolgreich auf ${limit} aktualisiert", "signInRequired": "Bitte melden Sie sich zu<PERSON>t bei Cursor an", "updateFailed": "Fehler beim Aktualisieren des Nutzungslimits"}, "errors": {"tokenNotFound": "Sitzungstoken nicht gefunden. Bitte melden Si<PERSON> sich bei Cursor an.", "apiError": "API-Anfrage fehlgeschlagen", "databaseError": "Datenbankzugriffsfehler", "networkError": "Netzwerkverbindungsfehler", "updateFailed": "Fehler beim Aktualisieren der Statistiken", "unknownError": "Ein unbekannter Fehler ist aufgetreten", "failedToCreateReport": "Fehler beim Erstellen des Berichts. Überprüfen Sie die Logs für Details.", "errorCreatingReport": "<PERSON><PERSON> beim Erstellen des Berichts: {error}"}, "time": {"day": "Tag", "days": "Tage", "hour": "Stunde", "hours": "Stunden", "minute": "Minute", "minutes": "Minuten", "second": "Sekunde", "seconds": "Sekunden", "ago": "vor", "refreshing": "Wird aktualisiert...", "lastUpdated": "Zuletzt aktualisiert"}, "currency": {"usd": "US-Dollar", "eur": "Euro", "gbp": "Britisches Pfund", "jpy": "Japanischer Yen", "aud": "Australischer Dollar", "cad": "Kanadischer Dollar", "chf": "Schweizer Franken", "cny": "Chinesis<PERSON>", "inr": "Indische Rupie", "mxn": "Mexikanischer Peso", "brl": "Brasilianischer Real", "rub": "Russischer Rubel", "krw": "Südkoreanischer Won", "sgd": "Singapur-Dollar", "nzd": "Neuseel<PERSON><PERSON><PERSON> Dollar", "try": "Türkische Lira", "zar": "Südafrikanischer Rand", "sek": "Schwedische Krone", "nok": "Norwegische Krone", "dkk": "Dänische Krone", "hkd": "Hongkong-Dollar", "twd": "Taiwan-Dollar", "php": "Philippinischer Peso", "thb": "Thailändi<PERSON> Baht", "idr": "Indonesische Rupiah", "vnd": "<PERSON><PERSON><PERSON>", "ils": "<PERSON><PERSON>", "aed": "VAE-Dirham", "sar": "Saudi-Riyal", "myr": "Malaysischer Ringgit", "pln": "Polnischer Złoty", "czk": "Tschechische Krone", "huf": "Ungarischer Forint", "ron": "Rumänischer Leu", "bgn": "Bulgarischer Lew", "hrk": "Kroatische Kuna", "egp": "Ägyptisches Pfund", "qar": "<PERSON><PERSON><PERSON>", "kwd": "Kuwaitische<PERSON> Dinar", "mad": "Marokkanischer Dirham"}}