// Start of Selection
/**
 * <AUTHOR>
 * HTTP request header utility for standardizing cursor.com API request headers
 * Addresses CORS and Origin validation issues
 */

import { HTTP } from '../constants';

/**
 * Generates standard cursor.com request headers to resolve 403 Invalid origin errors
 * @param token - Authentication token (WorkosCursorSessionToken)
 * @param isPostRequest - Whether this is a POST request, defaults to false
 * @returns Complete headers object
 */
export function createCursorHeaders(
  token: string,
  isPostRequest: boolean = false,
): Record<string, string> {
  const headers: Record<string, string> = {
    // Critical headers to resolve "Invalid origin for state-changing request" errors
    Origin: HTTP.HEADERS.ORIGIN,
    Referer: HTTP.HEADERS.REFERER,

    // Simulate real browser environment
    'User-Agent': HTTP.HEADERS.USER_AGENT,

    // Authentication
    Cookie: `WorkosCursorSessionToken=${token}`,

    // Standard request headers
    Accept: HTTP.HEADERS.ACCEPT,
    'Accept-Language': HTTP.HEADERS.ACCEPT_LANGUAGE,
    'Accept-Encoding': HTTP.HEADERS.ACCEPT_ENCODING,
    Connection: HTTP.HEADERS.CONNECTION,

    // Security policy related
    'Sec-Fetch-Dest': HTTP.HEADERS.SEC_FETCH_DEST,
    'Sec-Fetch-Mode': HTTP.HEADERS.SEC_FETCH_MODE,
    'Sec-Fetch-Site': HTTP.HEADERS.SEC_FETCH_SITE,
  };

  // POST requests require Content-Type header
  if (isPostRequest) {
    headers['Content-Type'] = HTTP.HEADERS.CONTENT_TYPE;
  }

  return headers;
}

/**
 * Creates enhanced error handling wrapper with detailed 403 error information
 * @param error - axios error object
 * @param context - Error context information
 */
export function enhanceApiError(error: any, context: string): Error {
  if (error.response?.status === 403) {
    const errorMessage = `[${context}] 403 Forbidden - ${error.response?.data?.error || 'Invalid origin for state-changing request'}`;
    const enhancedError = new Error(errorMessage);
    enhancedError.stack = error.stack;
    return enhancedError;
  }
  return error;
}

/**
 * Checks if the request URL is related to cursor.com
 * @param url - Request URL
 * @returns Whether it's a cursor.com request
 */
export function isCursorApiUrl(url: string): boolean {
  return url.startsWith('https://cursor.com/api/');
}
